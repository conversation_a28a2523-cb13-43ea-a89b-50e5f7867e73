
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI-Powered OPD Dashboard</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="alternate icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      // Configure Tailwind for dark mode
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {
            transitionProperty: {
              'colors': 'background-color, border-color, color, fill, stroke',
            }
          }
        }
      }
    </script>
    <script>
      // Initialize theme before page load to prevent flash
      (function() {
        const theme = localStorage.getItem('theme') || 'system';
        const isDark = theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches);
        if (isDark) {
          document.documentElement.classList.add('dark');
        }
      })();
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
      body {
        font-family: 'Inter', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
    </style>

<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-gray-50 dark:bg-gray-900">
    <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
