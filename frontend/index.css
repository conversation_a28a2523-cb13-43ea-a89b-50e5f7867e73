/* Tailwind CSS base styles - using CDN for now */
/* @tailwind base;
@tailwind components;
@tailwind utilities; */

/* Custom styles for smooth theme transitions */
* {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Ensure proper dark mode initialization */
html {
  color-scheme: light;
}

html.dark {
  color-scheme: dark;
}

/* Custom scrollbar styles for better dark mode support */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Focus styles for better accessibility */
.focus-visible {
  @apply outline-none ring-2 ring-offset-2 ring-indigo-500 dark:ring-offset-gray-900;
}

/* Custom animation for theme toggle */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}
