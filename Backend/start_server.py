#!/usr/bin/env python3
"""
Server startup script with better error handling
"""
import sys
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if all required dependencies are available"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'clickhouse_connect',
        'pandas',
        'numpy'
    ]

    missing = []
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✅ {package} is available")
        except ImportError:
            missing.append(package)
            logger.error(f"❌ {package} is missing")

    if missing:
        logger.error(f"Missing packages: {missing}")
        logger.error("Please install missing packages with: pip install " + " ".join(missing))
        return False

    return True

def test_imports():
    """Test if our custom modules can be imported"""
    try:
        from services.clickhouse_service import ClickHouseService
        logger.info("✅ ClickHouseService imported")
        
        from models.revenue_models import RevenueFilterParams
        logger.info("✅ Revenue models imported")
        
        from services.revenue_service import RevenueService
        logger.info("✅ RevenueService imported")
        
        from routes.revenue_routes import router
        logger.info("✅ Revenue routes imported")
        
        return True
    except Exception as e:
        logger.error(f"❌ Import error: {e}")
        return False

def start_server():
    """Start the FastAPI server"""
    try:
        import uvicorn
        from main import app
        
        logger.info("🚀 Starting FastAPI server...")
        logger.info("📍 Server will be available at: http://localhost:8000")
        logger.info("📖 API documentation at: http://localhost:8000/docs")
        logger.info("🏥 ClickHouse health check at: http://localhost:8000/health/clickhouse")
        
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=8000, 
            reload=True,
            log_level="info"
        )
        
    except Exception as e:
        logger.error(f"❌ Server startup failed: {e}")
        return False

def main():
    """Main startup function"""
    logger.info("🏥 Hospital Dashboard API - ClickHouse Integration")
    logger.info("=" * 60)
    
    # Check dependencies
    logger.info("📦 Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    
    # Test imports
    logger.info("📥 Testing imports...")
    if not test_imports():
        logger.warning("⚠️  Some imports failed, but continuing...")
    
    # Start server
    logger.info("🚀 Starting server...")
    start_server()

if __name__ == "__main__":
    main()
